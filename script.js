class WhatsAppVisualizer {
    constructor() {
        this.messages = [];
        this.currentRotation = 0;
        this.currentZoom = 1;
        this.audioPlayer = document.getElementById('audioPlayer');
        this.currentAudio = null;
        this.init();
    }

    async init() {
        this.loadConversation();
        this.renderMessages();
        this.setupEventListeners();
        this.setupScrollHandler();
    }

    loadConversation() {
        // Usa os dados incorporados do arquivo conversation-data.js
        if (typeof loadEmbeddedConversationData === 'function') {
            const conversationText = loadEmbeddedConversationData();
            this.parseConversation(conversationText);
        } else {
            console.error('Dados da conversa não encontrados');
        }
    }

    parseConversation(text) {
        const lines = text.split('\\n');
        let currentMessage = null;

        for (let line of lines) {
            line = line.trim();
            if (!line) continue;

            // Remove <br> tags
            line = line.replace(/<br>/g, '');

            // Check if it's a new message (starts with date pattern)
            const messageMatch = line.match(/^(\d{2}\/\d{2}\/\d{4}) (\d{2}:\d{2}) - (.*)$/);

            if (messageMatch) {
                // Save previous message if exists
                if (currentMessage) {
                    // Update message type after content is complete
                    currentMessage.type = this.getMessageType(currentMessage.content);
                    this.messages.push(currentMessage);
                }

                const [, date, time, rest] = messageMatch;

                // Check if it has a sender (contains colon after name)
                const senderMatch = rest.match(/^([^:]+): (.*)$/);

                if (senderMatch) {
                    // Regular message with sender
                    const [, sender, content] = senderMatch;
                    currentMessage = {
                        date,
                        time,
                        sender: sender.trim(),
                        content: content.trim()
                    };
                } else {
                    // System message without sender
                    currentMessage = {
                        date,
                        time,
                        sender: 'System',
                        content: rest.trim()
                    };
                }
            } else if (currentMessage && line) {
                // Continue previous message
                currentMessage.content += '\n' + line;
            }
        }

        // Add last message
        if (currentMessage) {
            currentMessage.type = this.getMessageType(currentMessage.content);
            this.messages.push(currentMessage);
        }

        console.log(`Parsed ${this.messages.length} messages`);
    }

    getMessageType(content) {
        // Check for system messages first
        if (content.includes('As mensagens e as ligações são protegidas') ||
            content.includes('criptografia de ponta a ponta')) {
            return 'system';
        }

        if (content.includes('(arquivo anexado)')) {
            if (content.includes('.opus') || content.includes('.mp3') || content.includes('AUD-')) {
                return 'audio';
            } else if (content.includes('.jpg') || content.includes('.png') ||
                       content.includes('IMAGEM') || content.includes('IMG-')) {
                return 'image';
            } else if (content.includes('.mp4') || content.includes('.avi') || content.includes('VID-')) {
                return 'video';
            } else if (content.includes('.vcf')) {
                return 'contact';
            }
        } else if (content === '<Mídia oculta>') {
            return 'hidden-media';
        } else if (content === 'Mensagem apagada') {
            return 'deleted';
        } else if (content.includes('http://') || content.includes('https://')) {
            return 'link';
        }
        return 'text';
    }

    renderMessages() {
        const container = document.getElementById('chatMessages');
        let currentDate = '';
        
        this.messages.forEach((message, index) => {
            // Add date separator if date changed
            if (message.date !== currentDate) {
                currentDate = message.date;
                const dateElement = this.createDateSeparator(message.date);
                container.appendChild(dateElement);
            }

            const messageElement = this.createMessageElement(message);
            container.appendChild(messageElement);
        });

        // Scroll to bottom
        container.scrollTop = container.scrollHeight;
    }

    createDateSeparator(date) {
        const dateDiv = document.createElement('div');
        dateDiv.className = 'date-separator';
        dateDiv.innerHTML = `<span class="date-badge">${this.formatDate(date)}</span>`;
        return dateDiv;
    }

    createMessageElement(message) {
        // Handle system messages differently
        if (message.type === 'system') {
            const systemDiv = document.createElement('div');
            systemDiv.className = 'system-message';
            systemDiv.innerHTML = this.formatText(message.content);
            return systemDiv;
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.sender === 'José Gabriel' ? 'sent' : 'received'}`;
        messageDiv.dataset.date = message.date;

        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';

        const content = document.createElement('div');
        content.className = 'message-content';

        // Render content based on message type
        switch (message.type) {
            case 'audio':
                content.appendChild(this.createAudioElement(message));
                break;
            case 'image':
                content.appendChild(this.createImageElement(message));
                break;
            case 'video':
                content.appendChild(this.createVideoElement(message));
                break;
            case 'contact':
                content.appendChild(this.createContactElement(message));
                break;
            case 'hidden-media':
                content.innerHTML = '<div class="hidden-media"><i class="fas fa-eye-slash"></i> Mídia oculta</div>';
                break;
            case 'deleted':
                content.innerHTML = '<div class="deleted-message">Mensagem apagada</div>';
                break;
            case 'link':
                content.innerHTML = this.formatLinks(message.content);
                break;
            default:
                content.innerHTML = this.formatText(message.content);
        }

        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = message.time;

        bubble.appendChild(content);
        bubble.appendChild(time);
        messageDiv.appendChild(bubble);

        return messageDiv;
    }

    createAudioElement(message) {
        const audioDiv = document.createElement('div');
        audioDiv.className = 'audio-message';

        const filename = this.extractFilename(message.content);
        const audioPath = `audios/${filename}`;

        audioDiv.innerHTML = `
            <button class="audio-play-btn" onclick="visualizer.playAudio('${audioPath}', this)">
                <i class="fas fa-play"></i>
            </button>
            <div class="audio-info">
                <div class="audio-progress">
                    <div class="audio-progress-bar"></div>
                </div>
                <div class="audio-duration">0:00</div>
            </div>
        `;

        return audioDiv;
    }

    createImageElement(message) {
        const imageDiv = document.createElement('div');
        imageDiv.className = 'media-message';

        let filename;
        if (message.content.includes('IMAGEM')) {
            // Extract from <a href="filename">IMAGEM</a> format
            const match = message.content.match(/href="([^"]+)"/);
            filename = match ? match[1] : '';
        } else {
            filename = this.extractFilename(message.content);
        }

        const imagePath = `media/${filename}`;
        
        const img = document.createElement('img');
        img.src = imagePath;
        img.alt = 'Imagem';
        img.onclick = () => this.openImageModal(imagePath);
        
        imageDiv.appendChild(img);
        return imageDiv;
    }

    createVideoElement(message) {
        const videoDiv = document.createElement('div');
        videoDiv.className = 'media-message';

        const filename = this.extractFilename(message.content);
        const videoPath = `media/${filename}`;

        const videoThumb = document.createElement('div');
        videoThumb.className = 'video-thumbnail';
        videoThumb.style.width = '200px';
        videoThumb.style.height = '150px';
        videoThumb.style.background = '#000';
        videoThumb.style.borderRadius = '8px';
        videoThumb.onclick = () => this.openVideoModal(videoPath);

        videoDiv.appendChild(videoThumb);
        return videoDiv;
    }

    createContactElement(message) {
        const contactDiv = document.createElement('div');
        contactDiv.className = 'contact-message';

        const filename = this.extractFilename(message.content);
        const contactName = filename.replace('.vcf', '');

        contactDiv.innerHTML = `
            <div class="contact-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="contact-info-msg">
                <div class="contact-name">${contactName}</div>
                <div class="contact-label">Contato</div>
            </div>
        `;

        return contactDiv;
    }

    extractFilename(content) {
        const match = content.match(/‎([^(]+)\s*\(arquivo anexado\)/);
        return match ? match[1].trim() : '';
    }

    formatText(text) {
        return text.replace(/\n/g, '<br>');
    }

    formatLinks(text) {
        const urlRegex = /(https?:\/\/[^\s]+)/g;
        return text.replace(urlRegex, '<a href="$1" target="_blank">$1</a>').replace(/\n/g, '<br>');
    }

    formatDate(dateStr) {
        const [day, month, year] = dateStr.split('/');
        const date = new Date(year, month - 1, day);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Hoje';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return 'Ontem';
        } else {
            return date.toLocaleDateString('pt-BR', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    }

    setupEventListeners() {
        // Image modal
        const imageModal = document.getElementById('imageModal');
        const videoModal = document.getElementById('videoModal');
        
        // Close modals
        document.querySelectorAll('.close-modal').forEach(btn => {
            btn.onclick = () => {
                imageModal.style.display = 'none';
                videoModal.style.display = 'none';
                this.resetImageTransform();
            };
        });

        // Close modal when clicking outside
        window.onclick = (event) => {
            if (event.target === imageModal) {
                imageModal.style.display = 'none';
                this.resetImageTransform();
            }
            if (event.target === videoModal) {
                videoModal.style.display = 'none';
            }
        };
    }

    setupScrollHandler() {
        const chatMessages = document.getElementById('chatMessages');
        const stickyDate = document.getElementById('stickyDate');
        
        chatMessages.addEventListener('scroll', () => {
            const messages = chatMessages.querySelectorAll('.message');
            let currentVisibleDate = '';
            
            for (let message of messages) {
                const rect = message.getBoundingClientRect();
                const containerRect = chatMessages.getBoundingClientRect();
                
                if (rect.top <= containerRect.top + 100) {
                    currentVisibleDate = message.dataset.date;
                }
            }
            
            if (currentVisibleDate) {
                stickyDate.textContent = this.formatDate(currentVisibleDate);
                stickyDate.style.display = 'block';
            }
        });
    }

    openImageModal(imagePath) {
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');
        modal.style.display = 'block';
        modalImg.src = imagePath;
        this.resetImageTransform();
    }

    openVideoModal(videoPath) {
        const modal = document.getElementById('videoModal');
        const modalVideo = document.getElementById('modalVideo');
        modal.style.display = 'block';
        modalVideo.src = videoPath;
    }

    resetImageTransform() {
        this.currentRotation = 0;
        this.currentZoom = 1;
        const img = document.getElementById('modalImage');
        img.style.transform = 'rotate(0deg) scale(1)';
    }

    async playAudio(audioPath, button) {
        const icon = button.querySelector('i');
        const progressBar = button.parentElement.querySelector('.audio-progress-bar');
        const durationElement = button.parentElement.querySelector('.audio-duration');

        if (this.currentAudio && this.currentAudio.src.includes(audioPath) && !this.currentAudio.paused) {
            // Pause current audio
            this.currentAudio.pause();
            icon.className = 'fas fa-play';
            return;
        }

        // Stop any currently playing audio
        if (this.currentAudio) {
            this.currentAudio.pause();
            document.querySelectorAll('.audio-play-btn i').forEach(i => i.className = 'fas fa-play');
        }

        // Play new audio
        this.audioPlayer.src = audioPath;
        this.currentAudio = this.audioPlayer;
        
        try {
            await this.audioPlayer.play();
            icon.className = 'fas fa-pause';

            this.audioPlayer.ontimeupdate = () => {
                const progress = (this.audioPlayer.currentTime / this.audioPlayer.duration) * 100;
                progressBar.style.width = progress + '%';
                durationElement.textContent = this.formatTime(this.audioPlayer.currentTime);
            };

            this.audioPlayer.onended = () => {
                icon.className = 'fas fa-play';
                progressBar.style.width = '0%';
                durationElement.textContent = '0:00';
            };

        } catch (error) {
            console.error('Erro ao reproduzir áudio:', error);
            icon.className = 'fas fa-play';
        }
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }
}

// Global functions for modal controls
function rotateImage() {
    visualizer.currentRotation += 90;
    const img = document.getElementById('modalImage');
    img.style.transform = `rotate(${visualizer.currentRotation}deg) scale(${visualizer.currentZoom})`;
}

function zoomImage(factor) {
    visualizer.currentZoom *= factor;
    const img = document.getElementById('modalImage');
    img.style.transform = `rotate(${visualizer.currentRotation}deg) scale(${visualizer.currentZoom})`;
}

// Initialize the visualizer
const visualizer = new WhatsAppVisualizer();
