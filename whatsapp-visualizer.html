<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Visualizer - Conversa com Dudu</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <div class="whatsapp-container">
        <!-- Header -->
        <div class="chat-header">
            <div class="contact-info">
                <div class="avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="contact-details">
                    <h3>Dudu</h3>
                    <span class="status">online</span>
                </div>
            </div>
            <div class="header-actions">
                <i class="fas fa-video"></i>
                <i class="fas fa-phone"></i>
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>

        <!-- Chat Messages Container -->
        <div class="chat-messages" id="chatMessages">
            <!-- Sticky Date Header -->
            <div class="sticky-date" id="stickyDate"></div>
            
            <!-- Messages will be loaded here -->
        </div>

        <!-- Input Area -->
        <div class="chat-input">
            <div class="input-container">
                <i class="fas fa-smile emoji-btn"></i>
                <i class="fas fa-paperclip attach-btn"></i>
                <input type="text" placeholder="Digite uma mensagem" disabled>
                <i class="fas fa-microphone mic-btn"></i>
            </div>
        </div>
    </div>

    <!-- Image Viewer Modal -->
    <div class="image-modal" id="imageModal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <img id="modalImage" src="" alt="">
            <div class="image-controls">
                <button class="rotate-btn" onclick="rotateImage()">
                    <i class="fas fa-redo"></i>
                </button>
                <button class="zoom-in-btn" onclick="zoomImage(1.2)">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button class="zoom-out-btn" onclick="zoomImage(0.8)">
                    <i class="fas fa-search-minus"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Video Player Modal -->
    <div class="video-modal" id="videoModal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <video id="modalVideo" controls>
                <source src="" type="video/mp4">
                Seu navegador não suporta o elemento de vídeo.
            </video>
        </div>
    </div>

    <!-- Audio Player -->
    <audio id="audioPlayer" preload="none"></audio>

    <script src="script.js"></script>
</body>
</html>
